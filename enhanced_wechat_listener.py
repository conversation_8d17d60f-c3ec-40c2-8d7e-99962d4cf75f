"""
增强版微信消息监听器
集成了聊天记录处理功能
"""

from wxauto import WeChat
from wxauto.msgs import FriendMessage
import time
import os
from datetime import datetime
from chat_record_handler import ChatRecordHandler

class EnhancedWeChatListener:
    """增强版微信监听器"""
    
    def __init__(self, target_nickname="启迪"):
        """
        初始化监听器
        
        Args:
            target_nickname: 要监听的聊天对象昵称
        """
        self.wx = WeChat()
        self.target_nickname = target_nickname
        self.chat_record_handler = ChatRecordHandler()
        self.message_count = 0
        self.start_time = datetime.now()
        
        # 创建日志目录
        self.log_dir = "message_logs"
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def on_message(self, msg, chat):
        """消息处理回调函数"""
        self.message_count += 1
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"\n[{self.message_count}] [{timestamp}] 收到消息 - 来自: {chat}")
        print(f"消息类型: {getattr(msg, 'type', 'unknown')}")
        print(f"消息属性: {getattr(msg, 'attr', 'unknown')}")
        
        try:
            # 1. 处理聊天记录消息
            if self.chat_record_handler.is_chat_record(msg.content):
                print("🔍 检测到聊天记录消息，开始处理...")
                success = self.chat_record_handler.process_chat_record_message(msg, chat)
                if success:
                    print("✅ 聊天记录处理完成")
                else:
                    print("❌ 聊天记录处理失败")
            
            # 2. 处理普通文本消息
            elif self._is_text_message(msg):
                print(f"📝 文本消息: {msg.content[:100]}{'...' if len(msg.content) > 100 else ''}")
                self._save_text_message(msg, chat, timestamp)
            
            # 3. 处理图片消息
            elif msg.type == 'image':
                print("🖼️ 图片消息，开始下载...")
                download_path = msg.download()
                print(f"✅ 图片已下载到: {download_path}")
                self._save_media_info(msg, chat, timestamp, download_path, "图片")
            
            # 4. 处理视频消息
            elif msg.type == 'video':
                print("🎥 视频消息，开始下载...")
                download_path = msg.download()
                print(f"✅ 视频已下载到: {download_path}")
                self._save_media_info(msg, chat, timestamp, download_path, "视频")
            
            # 5. 处理文件消息
            elif msg.type == 'file':
                print("📁 文件消息，开始下载...")
                download_path = msg.download()
                print(f"✅ 文件已下载到: {download_path}")
                self._save_media_info(msg, chat, timestamp, download_path, "文件")
            
            # 6. 处理其他类型消息
            else:
                print(f"❓ 其他类型消息: {msg.content[:100]}{'...' if len(msg.content) > 100 else ''}")
                self._save_other_message(msg, chat, timestamp)
            
            # 7. 自动回复（可选）
            if isinstance(msg, FriendMessage) and not self.chat_record_handler.is_chat_record(msg.content):
                # 只对非聊天记录的好友消息进行自动回复
                time.sleep(1)  # 稍微延迟，避免回复太快
                msg.quote('收到')
                print("↩️ 已自动回复")
        
        except Exception as e:
            print(f"❌ 处理消息时出错: {e}")
            self._log_error(e, msg, chat, timestamp)
    
    def _is_text_message(self, msg):
        """判断是否为普通文本消息"""
        return (msg.type == 'text' or 
                (hasattr(msg, 'type') and msg.type not in ('image', 'video', 'file')) and
                not self.chat_record_handler.is_chat_record(msg.content))
    
    def _save_text_message(self, msg, chat, timestamp):
        """保存文本消息"""
        log_file = os.path.join(self.log_dir, f"text_messages_{datetime.now().strftime('%Y%m%d')}.txt")
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {chat}: {msg.content}\n")
    
    def _save_media_info(self, msg, chat, timestamp, download_path, media_type):
        """保存媒体消息信息"""
        log_file = os.path.join(self.log_dir, f"media_messages_{datetime.now().strftime('%Y%m%d')}.txt")
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {chat} - {media_type}: {download_path}\n")
    
    def _save_other_message(self, msg, chat, timestamp):
        """保存其他类型消息"""
        log_file = os.path.join(self.log_dir, f"other_messages_{datetime.now().strftime('%Y%m%d')}.txt")
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {chat} - {msg.type}: {msg.content}\n")
    
    def _log_error(self, error, msg, chat, timestamp):
        """记录错误信息"""
        error_file = os.path.join(self.log_dir, f"errors_{datetime.now().strftime('%Y%m%d')}.txt")
        with open(error_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] 错误: {error}\n")
            f.write(f"聊天: {chat}\n")
            f.write(f"消息类型: {getattr(msg, 'type', 'unknown')}\n")
            f.write(f"消息内容: {msg.content[:200]}...\n")
            f.write("-" * 50 + "\n")
    
    def start_listening(self):
        """开始监听"""
        print("🚀 启动增强版微信消息监听器")
        print(f"📱 监听对象: {self.target_nickname}")
        print(f"📁 日志目录: {self.log_dir}")
        print(f"📁 聊天记录目录: {self.chat_record_handler.save_directory}")
        print("\n支持的功能:")
        print("✅ 聊天记录自动解析和保存")
        print("✅ 文本消息记录")
        print("✅ 图片/视频/文件自动下载")
        print("✅ 自动回复")
        print("✅ 错误日志记录")
        print("\n按 Ctrl+C 停止监听\n")
        
        try:
            # 添加监听
            self.wx.AddListenChat(nickname=self.target_nickname, callback=self.on_message)
            
            # 保持运行
            self.wx.KeepRunning()
            
        except KeyboardInterrupt:
            self.stop_listening()
        except Exception as e:
            print(f"❌ 监听过程中出错: {e}")
            self.stop_listening()
    
    def stop_listening(self):
        """停止监听"""
        print("\n🛑 正在停止监听...")
        
        try:
            self.wx.RemoveListenChat(nickname=self.target_nickname)
            
            # 显示统计信息
            end_time = datetime.now()
            duration = end_time - self.start_time
            
            print("\n📊 监听统计:")
            print(f"⏱️ 运行时间: {duration}")
            print(f"📨 处理消息数: {self.message_count}")
            print(f"📁 日志目录: {self.log_dir}")
            print(f"📁 聊天记录目录: {self.chat_record_handler.save_directory}")
            
        except Exception as e:
            print(f"❌ 停止监听时出错: {e}")
        
        print("✅ 监听已停止")
    
    def get_statistics(self):
        """获取统计信息"""
        return {
            'message_count': self.message_count,
            'start_time': self.start_time,
            'current_time': datetime.now(),
            'log_directory': self.log_dir,
            'chat_records_directory': self.chat_record_handler.save_directory
        }

def main():
    """主函数"""
    # 创建监听器实例
    listener = EnhancedWeChatListener(target_nickname="启迪")
    
    # 开始监听
    listener.start_listening()

if __name__ == "__main__":
    main()
