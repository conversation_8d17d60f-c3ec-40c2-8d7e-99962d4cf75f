2025-10-10 09:36:13 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 09:38:21 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 09:38:59 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 09:39:32 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 09:39:32 [wxauto] [DEBUG] [sessionbox.py:70]  切换聊天窗口: 文件传输助手, False, False, 0.5
2025-10-10 09:39:33 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 09:39:33 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 9
2025-10-10 09:45:14 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 09:45:14 [wxauto] [DEBUG] [sessionbox.py:70]  切换聊天窗口: 文件传输助手, False, False, 0.5
2025-10-10 09:45:16 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 09:45:16 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 09:45:16 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 09:45:16 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 09:45:33 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 09:45:34 [wxauto] [DEBUG] [sessionbox.py:70]  切换聊天窗口: 文件传输助手, False, False, 0.5
2025-10-10 09:45:35 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 09:45:35 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 09:45:35 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 09:45:35 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 09:45:36 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:06:45 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 10:06:45 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:06:45 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:06:45 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 10:06:45 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:06:45 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:08:54 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 10:08:54 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:08:54 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:08:54 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 10:08:54 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:08:54 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:09:11 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 10:09:11 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:09:11 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:09:11 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 10:10:04 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 10:10:04 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:10:04 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:10:04 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 10:10:04 [wxauto] [DEBUG] [msg.py:79]  content: 【运维单位】水务科技（运维一组）
【巡检日期】2025年10月10日
【巡检时段】08:00-18:00
一组：
巡检泵房:梅林一村，华茂苑，艺丰花园，莱英达，特皓苑，新阁小区1,2栋
巡检人员:
李星浩：14761051713 
罗嘉翔:  13148799004
二组：
巡检泵房：长城一花园，长城二花园，海馨苑，华富村，百花园二期，国城花园。
巡检人员:
洪少滨：19146443986
罗嘉翔:  13148799004
三组：
巡检泵房：凯欣园、宝鸿苑、香江西苑、福瑞阁 华富综合楼 机场生活区 景蜜村 景田天健花园 景茗苑 天明居 景尚雅苑
巡检人员：
陈炜城13724328229
周前乐13714014482
四组：
巡检泵房: 绿茵阁、福雅园、福涛东苑、时代星居、
巡检人员:
张浩：18323339952
赖嘉豪：17278651209
五组：
巡检泵房:南光捷佳大厦、嘉汇新城、富豪大厦、国企大厦、台湾花园、御河堤、金宝城、锦龙花园
巡检人员:
吴志浩：13714810803
吴俊彬：15013934412, length: 8
2025-10-10 10:10:25 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 10:10:25 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:10:25 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:10:25 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 10:10:25 [wxauto] [DEBUG] [msg.py:79]  content: 【运维单位】水务科技（运维一组）
【巡检日期】2025年10月10日
【巡检时段】08:00-18:00
一组：
巡检泵房:梅林一村，华茂苑，艺丰花园，莱英达，特皓苑，新阁小区1,2栋
巡检人员:
李星浩：14761051713 
罗嘉翔:  13148799004
二组：
巡检泵房：长城一花园，长城二花园，海馨苑，华富村，百花园二期，国城花园。
巡检人员:
洪少滨：19146443986
罗嘉翔:  13148799004
三组：
巡检泵房：凯欣园、宝鸿苑、香江西苑、福瑞阁 华富综合楼 机场生活区 景蜜村 景田天健花园 景茗苑 天明居 景尚雅苑
巡检人员：
陈炜城13724328229
周前乐13714014482
四组：
巡检泵房: 绿茵阁、福雅园、福涛东苑、时代星居、
巡检人员:
张浩：18323339952
赖嘉豪：17278651209
五组：
巡检泵房:南光捷佳大厦、嘉汇新城、富豪大厦、国企大厦、台湾花园、御河堤、金宝城、锦龙花园
巡检人员:
吴志浩：13714810803
吴俊彬：15013934412, length: 8
2025-10-10 10:11:57 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 10:11:57 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:11:57 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:11:57 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 10:11:57 [wxauto] [DEBUG] [msg.py:79]  content: 【运维单位】水务科技（运维一组）
【巡检日期】2025年10月10日
【巡检时段】08:00-18:00
一组：
巡检泵房:梅林一村，华茂苑，艺丰花园，莱英达，特皓苑，新阁小区1,2栋
巡检人员:
李星浩：14761051713 
罗嘉翔:  13148799004
二组：
巡检泵房：长城一花园，长城二花园，海馨苑，华富村，百花园二期，国城花园。
巡检人员:
洪少滨：19146443986
罗嘉翔:  13148799004
三组：
巡检泵房：凯欣园、宝鸿苑、香江西苑、福瑞阁 华富综合楼 机场生活区 景蜜村 景田天健花园 景茗苑 天明居 景尚雅苑
巡检人员：
陈炜城13724328229
周前乐13714014482
四组：
巡检泵房: 绿茵阁、福雅园、福涛东苑、时代星居、
巡检人员:
张浩：18323339952
赖嘉豪：17278651209
五组：
巡检泵房:南光捷佳大厦、嘉汇新城、富豪大厦、国企大厦、台湾花园、御河堤、金宝城、锦龙花园
巡检人员:
吴志浩：13714810803
吴俊彬：15013934412, length: 8
2025-10-10 10:13:33 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 10:13:34 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:13:34 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 10:13:34 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 10:13:34 [wxauto] [DEBUG] [msg.py:79]  content: 【运维单位】水务科技（运维一组）
【巡检日期】2025年10月10日
【巡检时段】08:00-18:00
一组：
巡检泵房:梅林一村，华茂苑，艺丰花园，莱英达，特皓苑，新阁小区1,2栋
巡检人员:
李星浩：14761051713 
罗嘉翔:  13148799004
二组：
巡检泵房：长城一花园，长城二花园，海馨苑，华富村，百花园二期，国城花园。
巡检人员:
洪少滨：19146443986
罗嘉翔:  13148799004
三组：
巡检泵房：凯欣园、宝鸿苑、香江西苑、福瑞阁 华富综合楼 机场生活区 景蜜村 景田天健花园 景茗苑 天明居 景尚雅苑
巡检人员：
陈炜城13724328229
周前乐13714014482
四组：
巡检泵房: 绿茵阁、福雅园、福涛东苑、时代星居、
巡检人员:
张浩：18323339952
赖嘉豪：17278651209
五组：
巡检泵房:南光捷佳大厦、嘉汇新城、富豪大厦、国企大厦、台湾花园、御河堤、金宝城、锦龙花园
巡检人员:
吴志浩：13714810803
吴俊彬：15013934412, length: 8
2025-10-10 10:13:40 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 10:13:40 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:13:40 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:13:40 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 10:13:40 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:13:40 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:14:33 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 10:14:33 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:14:33 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:14:33 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 10:14:33 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 10:14:33 [wxauto] [DEBUG] [msg.py:79]  content: 这是通过wxauto发给你的消息！, length: 8
2025-10-10 11:30:51 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 11:30:51 [wxauto] [DEBUG] [sessionbox.py:70]  切换聊天窗口: 启迪, False, False, 0.5
2025-10-10 11:30:52 [wxauto] [DEBUG] [main.py:201]  启迪 切换到聊天窗口: 启迪
2025-10-10 11:30:52 [wxauto] [DEBUG] [sessionbox.py:154]  打开独立窗口: 启迪
2025-10-10 11:30:52 [wxauto] [DEBUG] [sessionbox.py:158]  找到会话: 启迪
2025-10-10 11:31:04 [wxauto] [DEBUG] [wx.py:216]  窗口 启迪 已关闭，移除监听
2025-10-10 11:31:25 [wxauto] [DEBUG] [wx.py:358]  wxauto("小小蒋") shutdown
2025-10-10 11:31:29 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 11:31:29 [wxauto] [DEBUG] [sessionbox.py:70]  切换聊天窗口: 启迪, False, False, 0.5
2025-10-10 11:31:29 [wxauto] [DEBUG] [main.py:201]  启迪 切换到聊天窗口: 启迪
2025-10-10 11:31:29 [wxauto] [DEBUG] [sessionbox.py:154]  打开独立窗口: 启迪
2025-10-10 11:31:29 [wxauto] [DEBUG] [sessionbox.py:158]  找到会话: 启迪
2025-10-10 11:31:41 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 11:31:41 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:31:41 [wxauto] [DEBUG] [wx.py:54]  监听消息回调发生错误：Traceback (most recent call last):
  File "E:\software\Lib\site-packages\wxauto\wx.py", line 52, in _safe_callback
    callback(msg, chat)
    ~~~~~~~~^^^^^^^^^^^
  File "F:\Win\wxlg\index.py", line 10, in on_message
    with open('"F:\Win\openlist.txt"', 'a', encoding='utf-8') as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
OSError: [Errno 22] Invalid argument: '"F:\\Win\\openlist.txt"'

2025-10-10 11:31:41 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 哈喽
2025-10-10 11:31:41 [wxauto] [DEBUG] [wx.py:54]  监听消息回调发生错误：Traceback (most recent call last):
  File "E:\software\Lib\site-packages\wxauto\wx.py", line 52, in _safe_callback
    callback(msg, chat)
    ~~~~~~~~^^^^^^^^^^^
  File "F:\Win\wxlg\index.py", line 10, in on_message
    with open('"F:\Win\openlist.txt"', 'a', encoding='utf-8') as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
OSError: [Errno 22] Invalid argument: '"F:\\Win\\openlist.txt"'

2025-10-10 11:32:01 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽哈喽哈喽哈喽, length: 8
2025-10-10 11:32:01 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:32:02 [wxauto] [DEBUG] [wx.py:54]  监听消息回调发生错误：Traceback (most recent call last):
  File "E:\software\Lib\site-packages\wxauto\wx.py", line 52, in _safe_callback
    callback(msg, chat)
    ~~~~~~~~^^^^^^^^^^^
  File "F:\Win\wxlg\index.py", line 10, in on_message
    with open('"F:\Win\openlist.txt"', 'a', encoding='utf-8') as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
OSError: [Errno 22] Invalid argument: '"F:\\Win\\openlist.txt"'

2025-10-10 11:32:02 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 哈喽哈喽哈喽哈喽
2025-10-10 11:32:02 [wxauto] [DEBUG] [wx.py:54]  监听消息回调发生错误：Traceback (most recent call last):
  File "E:\software\Lib\site-packages\wxauto\wx.py", line 52, in _safe_callback
    callback(msg, chat)
    ~~~~~~~~^^^^^^^^^^^
  File "F:\Win\wxlg\index.py", line 10, in on_message
    with open('"F:\Win\openlist.txt"', 'a', encoding='utf-8') as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
OSError: [Errno 22] Invalid argument: '"F:\\Win\\openlist.txt"'

2025-10-10 11:32:23 [wxauto] [DEBUG] [msg.py:79]  content: 萨, length: 8
2025-10-10 11:32:23 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:32:23 [wxauto] [DEBUG] [wx.py:54]  监听消息回调发生错误：Traceback (most recent call last):
  File "E:\software\Lib\site-packages\wxauto\wx.py", line 52, in _safe_callback
    callback(msg, chat)
    ~~~~~~~~^^^^^^^^^^^
  File "F:\Win\wxlg\index.py", line 10, in on_message
    with open('"F:\Win\openlist.txt"', 'a', encoding='utf-8') as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
OSError: [Errno 22] Invalid argument: '"F:\\Win\\openlist.txt"'

2025-10-10 11:32:23 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 萨
2025-10-10 11:32:23 [wxauto] [DEBUG] [wx.py:54]  监听消息回调发生错误：Traceback (most recent call last):
  File "E:\software\Lib\site-packages\wxauto\wx.py", line 52, in _safe_callback
    callback(msg, chat)
    ~~~~~~~~^^^^^^^^^^^
  File "F:\Win\wxlg\index.py", line 10, in on_message
    with open('"F:\Win\openlist.txt"', 'a', encoding='utf-8') as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
OSError: [Errno 22] Invalid argument: '"F:\\Win\\openlist.txt"'

2025-10-10 11:33:20 [wxauto] [DEBUG] [wx.py:358]  wxauto("小小蒋") shutdown
2025-10-10 11:33:24 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-10-10 11:33:24 [wxauto] [DEBUG] [main.py:197]  启迪 获取到已存在的子窗口: <wxauto - WeChatSubWnd object("启迪")>
2025-10-10 11:33:42 [wxauto] [DEBUG] [msg.py:79]  content: 请我, length: 8
2025-10-10 11:33:42 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:33:42 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 请我
2025-10-10 11:33:45 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : 请我, length: 20
2025-10-10 11:33:45 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 11:34:00 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 11:34:00 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:34:00 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 哈喽
2025-10-10 11:34:02 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : 哈喽, length: 20
2025-10-10 11:34:02 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 11:34:37 [wxauto] [DEBUG] [msg.py:79]  content: 【运维单位】水务科技（运维一组）
【巡检日期】2025年10月10日
【巡检时段】08:00-18:00
一组：
巡检泵房:梅林一村，华茂苑，艺丰花园，莱英达，特皓苑，新阁小区1,2栋
巡检人员:
李星浩：14761051713 
罗嘉翔:  13148799004
二组：
巡检泵房：长城一花园，长城二花园，海馨苑，华富村，百花园二期，国城花园。
巡检人员:
洪少滨：19146443986
罗嘉翔:  13148799004
三组：
巡检泵房：凯欣园、宝鸿苑、香江西苑、福瑞阁 华富综合楼 机场生活区 景蜜村 景田天健花园 景茗苑 天明居 景尚雅苑
巡检人员：
陈炜城13724328229
周前乐13714014482
四组：
巡检泵房: 绿茵阁、福雅园、福涛东苑、时代星居、
巡检人员:
张浩：18323339952
赖嘉豪：17278651209
五组：
巡检泵房:南光捷佳大厦、嘉汇新城、富豪大厦、国企大厦、台湾花园、御河堤、金宝城、锦龙花园
巡检人员:
吴志浩：13714810803
吴俊彬：15013934412, length: 8
2025-10-10 11:34:37 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:34:37 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 【运维单位】水务科技（运维一组）
【巡检日期】2025年10月10日
【巡检时段】08:00-18:00
一组：
巡检泵房:梅林一村，华茂苑，艺丰花园，莱英达，特皓苑，新阁小区1,2栋
巡检人员:
李星浩：14761051713 
罗嘉翔:  13148799004
二组：
巡检泵房：长城一花园，长城二花园，海馨苑，华富村，百花园二期，国城花园。
巡检人员:
洪少滨：19146443986
罗嘉翔:  13148799004
三组：
巡检泵房：凯欣园、宝鸿苑、香江西苑、福瑞阁 华富综合楼 机场生活区 景蜜村 景田天健花园 景茗苑 天明居 景尚雅苑
巡检人员：
陈炜城13724328229
周前乐13714014482
四组：
巡检泵房: 绿茵阁、福雅园、福涛东苑、时代星居、
巡检人员:
张浩：18323339952
赖嘉豪：17278651209
五组：
巡检泵房:南光捷佳大厦、嘉汇新城、富豪大厦、国企大厦、台湾花园、御河堤、金宝城、锦龙花园
巡检人员:
吴志浩：13714810803
吴俊彬：15013934412
2025-10-10 11:34:41 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : 【运维单位】水务科技（运维一组）
【巡检日期】2025年10月10日
【巡检时段】08:00-18:00
一组：
巡检泵房:梅林一村，华茂苑，艺丰花园，莱英达，特皓苑，新阁小区1,2栋
巡检人员:
李星浩：14761051713 
罗嘉翔:  13148799004
二组：
巡检泵房：长城一花园，长城二花园，海馨苑，华富村，百花园二期，国城花园。
巡检人员:
洪少滨：19146443986
罗嘉翔:  13148799004
三组：
巡检泵房：凯欣园、宝鸿苑、香江西苑、福瑞阁 华富综合楼 机场生活区 景蜜村 景田天健花园 景茗苑 天明居 景尚雅苑
巡检人员：
陈炜城13724328229
周前乐13714014482
四组：
巡检泵房: 绿茵阁、福雅园、福涛东苑、时代星居、
巡检人员:
张浩：18323339952
赖嘉豪：17278651209
五组：
巡检泵房:南光捷佳大厦、嘉汇新城、富豪大厦、国企大厦、台湾花园、御河堤、金宝城、锦龙花园
巡检人员:
吴志浩：13714810803
吴俊彬：15013934412, length: 20
2025-10-10 11:34:41 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 11:35:12 [wxauto] [DEBUG] [msg.py:79]  content: [聊天记录], length: 17
2025-10-10 11:35:12 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:35:12 [wxauto] [DEBUG] [wx.py:224]  [friend other]获取到新消息：启迪 - [聊天记录]
2025-10-10 11:35:14 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : [聊天记录]夏日￣^￣゜寒風与文件传输助手的聊天记录, length: 20
2025-10-10 11:35:14 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 11:36:11 [wxauto] [DEBUG] [msg.py:79]  content: [图片], length: 9
2025-10-10 11:36:11 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:36:11 [wxauto] [DEBUG] [wx.py:224]  [friend image]获取到新消息：启迪 - [图片]
2025-10-10 11:36:15 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : [图片], length: 23
2025-10-10 11:36:15 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 11:37:10 [wxauto] [DEBUG] [msg.py:79]  content: [图片], length: 9
2025-10-10 11:37:10 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:37:10 [wxauto] [DEBUG] [wx.py:224]  [friend image]获取到新消息：启迪 - [图片]
2025-10-10 11:37:14 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : [图片], length: 23
2025-10-10 11:37:14 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 11:37:34 [wxauto] [DEBUG] [msg.py:79]  content: 哈喽, length: 8
2025-10-10 11:37:34 [wxauto] [DEBUG] [wx.py:224]  [self text]获取到新消息：启迪 - 哈喽
2025-10-10 11:38:06 [wxauto] [DEBUG] [msg.py:79]  content: 我是你大爷, length: 8
2025-10-10 11:38:06 [wxauto] [DEBUG] [wx.py:224]  [self text]获取到新消息：启迪 - 我是你大爷
2025-10-10 11:38:26 [wxauto] [DEBUG] [msg.py:79]  content: 呵呵呵呵交际, length: 8
2025-10-10 11:38:26 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:38:26 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 呵呵呵呵交际
2025-10-10 11:38:28 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : 呵呵呵呵交际, length: 20
2025-10-10 11:38:28 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 11:49:24 [wxauto] [DEBUG] [msg.py:79]  content: 回家啃, length: 8
2025-10-10 11:49:24 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:49:24 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 回家啃
2025-10-10 11:49:27 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : 回家啃, length: 20
2025-10-10 11:49:27 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 11:54:06 [wxauto] [DEBUG] [msg.py:79]  content: 。。。。这, length: 8
2025-10-10 11:54:06 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:54:06 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 。。。。这
2025-10-10 11:54:09 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : 。。。。这, length: 20
2025-10-10 11:54:09 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 11:54:18 [wxauto] [DEBUG] [msg.py:79]  content: 这感觉就像, length: 8
2025-10-10 11:54:18 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 11:54:18 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 这感觉就像
2025-10-10 11:54:20 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : 这感觉就像, length: 20
2025-10-10 11:54:20 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 14:12:58 [wxauto] [DEBUG] [msg.py:79]  content: ，, length: 8
2025-10-10 14:12:58 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 14:12:58 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - ，
2025-10-10 14:13:02 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : ，, length: 20
2025-10-10 14:13:02 [wxauto] [DEBUG] [wx.py:224]  [time base]获取到新消息：启迪 - 14:13
2025-10-10 14:13:02 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
2025-10-10 14:14:21 [wxauto] [DEBUG] [msg.py:79]  content: 怎么处理, length: 8
2025-10-10 14:14:21 [wxauto] [DEBUG] [wx.py:224]  [system base]获取到新消息：启迪 - 以下为新消息
2025-10-10 14:14:21 [wxauto] [DEBUG] [wx.py:224]  [friend text]获取到新消息：启迪 - 怎么处理
2025-10-10 14:14:23 [wxauto] [DEBUG] [msg.py:79]  content: 收到
引用  的消息 : 怎么处理, length: 20
2025-10-10 14:14:23 [wxauto] [DEBUG] [wx.py:224]  [self quote]获取到新消息：启迪 - 收到
