"""
微信聊天记录处理器
专门用于处理和解析微信聊天记录消息
"""

import json
import re
import os
from datetime import datetime
from typing import Dict, List, Any

class ChatRecordHandler:
    """聊天记录处理器"""
    
    def __init__(self, save_directory="chat_records"):
        """
        初始化聊天记录处理器
        
        Args:
            save_directory: 保存聊天记录的目录
        """
        self.save_directory = save_directory
        self.ensure_directory_exists()
    
    def ensure_directory_exists(self):
        """确保保存目录存在"""
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)
            print(f"创建聊天记录保存目录: {self.save_directory}")
    
    def is_chat_record(self, msg_content: str) -> bool:
        """
        判断是否为聊天记录消息
        
        Args:
            msg_content: 消息内容
            
        Returns:
            bool: 是否为聊天记录
        """
        # 检查是否包含聊天记录标识
        chat_record_indicators = [
            '[聊天记录]',
            '聊天记录',
            '的聊天记录',
            'chat record',
            'Chat Record'
        ]
        
        return any(indicator in msg_content for indicator in chat_record_indicators)
    
    def parse_chat_record(self, msg_content: str) -> Dict[str, Any]:
        """
        解析聊天记录内容
        
        Args:
            msg_content: 聊天记录消息内容
            
        Returns:
            Dict: 解析后的聊天记录信息
        """
        parsed_data = {
            'timestamp': datetime.now().isoformat(),
            'raw_content': msg_content,
            'participants': [],
            'messages': [],
            'metadata': {}
        }
        
        try:
            # 尝试提取参与者信息
            participants = self._extract_participants(msg_content)
            parsed_data['participants'] = participants
            
            # 尝试提取消息内容
            messages = self._extract_messages(msg_content)
            parsed_data['messages'] = messages
            
            # 提取元数据
            metadata = self._extract_metadata(msg_content)
            parsed_data['metadata'] = metadata
            
        except Exception as e:
            print(f"解析聊天记录时出错: {e}")
            parsed_data['parse_error'] = str(e)
        
        return parsed_data
    
    def _extract_participants(self, content: str) -> List[str]:
        """提取聊天参与者"""
        participants = []
        
        # 常见的参与者提取模式
        patterns = [
            r'与(.+?)的聊天记录',
            r'(.+?)与(.+?)的聊天记录',
            r'聊天记录.*?参与者[：:]\s*(.+)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            if matches:
                for match in matches:
                    if isinstance(match, tuple):
                        participants.extend(match)
                    else:
                        participants.append(match)
                break
        
        # 清理和去重
        participants = [p.strip() for p in participants if p.strip()]
        return list(set(participants))
    
    def _extract_messages(self, content: str) -> List[Dict[str, str]]:
        """提取聊天消息"""
        messages = []
        
        # 按行分割内容
        lines = content.split('\n')
        
        current_message = {}
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 尝试匹配时间戳模式
            time_patterns = [
                r'(\d{1,2}:\d{2})',
                r'(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2})',
                r'(\d{1,2}月\d{1,2}日\s+\d{1,2}:\d{2})',
            ]
            
            time_match = None
            for pattern in time_patterns:
                time_match = re.search(pattern, line)
                if time_match:
                    break
            
            if time_match:
                # 如果有之前的消息，保存它
                if current_message:
                    messages.append(current_message)
                
                # 开始新消息
                current_message = {
                    'time': time_match.group(1),
                    'content': line,
                    'raw_line': line
                }
            else:
                # 继续当前消息
                if current_message:
                    current_message['content'] += '\n' + line
                else:
                    # 没有时间戳的独立行
                    messages.append({
                        'time': '',
                        'content': line,
                        'raw_line': line
                    })
        
        # 添加最后一条消息
        if current_message:
            messages.append(current_message)
        
        return messages
    
    def _extract_metadata(self, content: str) -> Dict[str, Any]:
        """提取元数据"""
        metadata = {}
        
        # 提取日期信息
        date_patterns = [
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{1,2}月\d{1,2}日)',
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, content)
            if match:
                metadata['date'] = match.group(1)
                break
        
        # 统计信息
        metadata['total_lines'] = len(content.split('\n'))
        metadata['total_chars'] = len(content)
        
        return metadata
    
    def save_chat_record(self, parsed_data: Dict[str, Any], source_chat: str) -> str:
        """
        保存聊天记录到文件
        
        Args:
            parsed_data: 解析后的聊天记录数据
            source_chat: 来源聊天窗口
            
        Returns:
            str: 保存的文件路径
        """
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_chat_name = "".join(c for c in source_chat if c.isalnum() or c in (' ', '-', '_')).rstrip()
        
        # 保存原始文本文件
        txt_filename = f"{safe_chat_name}_聊天记录_{timestamp}.txt"
        txt_filepath = os.path.join(self.save_directory, txt_filename)
        
        with open(txt_filepath, 'w', encoding='utf-8') as f:
            f.write(f"聊天记录保存时间: {parsed_data['timestamp']}\n")
            f.write(f"来源聊天: {source_chat}\n")
            f.write(f"参与者: {', '.join(parsed_data['participants'])}\n")
            f.write("=" * 60 + "\n\n")
            f.write("原始内容:\n")
            f.write(parsed_data['raw_content'])
            f.write("\n\n" + "=" * 60 + "\n\n")
            
            if parsed_data['messages']:
                f.write("解析后的消息:\n")
                for i, msg in enumerate(parsed_data['messages'], 1):
                    f.write(f"{i}. [{msg['time']}] {msg['content']}\n")
        
        # 保存JSON格式的结构化数据
        json_filename = f"{safe_chat_name}_聊天记录_{timestamp}.json"
        json_filepath = os.path.join(self.save_directory, json_filename)
        
        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(parsed_data, f, ensure_ascii=False, indent=2)
        
        print(f"聊天记录已保存:")
        print(f"  文本文件: {txt_filepath}")
        print(f"  JSON文件: {json_filepath}")
        
        return txt_filepath
    
    def process_chat_record_message(self, msg, source_chat: str):
        """
        处理聊天记录消息的主要方法
        
        Args:
            msg: wxauto消息对象
            source_chat: 来源聊天窗口名称
        """
        if not self.is_chat_record(msg.content):
            return False
        
        print(f"检测到聊天记录消息，来自: {source_chat}")
        
        # 解析聊天记录
        parsed_data = self.parse_chat_record(msg.content)
        
        # 保存聊天记录
        saved_path = self.save_chat_record(parsed_data, source_chat)
        
        # 打印统计信息
        print(f"聊天记录统计:")
        print(f"  参与者数量: {len(parsed_data['participants'])}")
        print(f"  消息数量: {len(parsed_data['messages'])}")
        print(f"  总字符数: {parsed_data['metadata'].get('total_chars', 0)}")
        
        return True

# 使用示例
if __name__ == "__main__":
    # 创建聊天记录处理器
    handler = ChatRecordHandler()
    
    # 测试聊天记录内容
    test_content = """[聊天记录]夏日￣^￣゜寒風与文件传输助手的聊天记录
    
2025年10月10日 14:30
夏日￣^￣゜寒風: 你好
14:31
文件传输助手: 你好，有什么可以帮助你的吗？
14:32
夏日￣^￣゜寒風: 我想了解一下这个功能
14:33
文件传输助手: 好的，我来为你介绍一下"""
    
    # 模拟消息对象
    class MockMessage:
        def __init__(self, content):
            self.content = content
    
    mock_msg = MockMessage(test_content)
    
    # 处理聊天记录
    handler.process_chat_record_message(mock_msg, "测试聊天")
