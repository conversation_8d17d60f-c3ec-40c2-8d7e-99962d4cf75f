from wxauto import WeChat
from wxauto.msgs import FriendMessage
import time
import json
import os
from datetime import datetime

wx = WeChat()

# 消息处理函数
def on_message(msg, chat):
    # 获取当前时间戳
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 处理聊天记录消息
    if msg.content == '[聊天记录]' or '聊天记录' in msg.content:
        print(f"[{timestamp}] 检测到聊天记录消息")
        save_chat_record(msg, chat, timestamp)

    # 处理普通文本消息
    elif msg.type == 'text' or (hasattr(msg, 'type') and msg.type not in ('image', 'video', 'file')):
        save_text_message(msg, chat, timestamp)

    # 处理图片和视频消息
    elif msg.type in ('image', 'video'):
        print(f"[{timestamp}] 检测到{msg.type}消息，开始下载...")
        download_path = msg.download()
        print(f"[{timestamp}] {msg.type}已下载到: {download_path}")
        save_media_message(msg, chat, timestamp, download_path)

    # 自动回复收到（可选）
    if isinstance(msg, FriendMessage):
        msg.quote('收到')

def save_chat_record(msg, chat, timestamp):
    """保存聊天记录消息"""
    try:
        # 创建聊天记录目录
        chat_records_dir = 'chat_records'
        if not os.path.exists(chat_records_dir):
            os.makedirs(chat_records_dir)

        # 生成文件名（使用时间戳避免重复）
        safe_chat_name = "".join(c for c in chat if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_chat_name}_聊天记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        filepath = os.path.join(chat_records_dir, filename)

        # 保存聊天记录内容
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"聊天记录保存时间: {timestamp}\n")
            f.write(f"来源聊天: {chat}\n")
            f.write("=" * 50 + "\n")
            f.write(msg.content + "\n")

        print(f"[{timestamp}] 聊天记录已保存到: {filepath}")

        # 同时保存到总的消息日志
        log_message_to_file(f"[聊天记录] {chat}", msg.content, timestamp)

    except Exception as e:
        print(f"[{timestamp}] 保存聊天记录时出错: {e}")

def save_text_message(msg, chat, timestamp):
    """保存普通文本消息"""
    try:
        # 保存到总的消息日志
        log_message_to_file(f"[文本] {chat}", msg.content, timestamp)
        print(f"[{timestamp}] 文本消息已记录: {msg.content[:50]}...")

    except Exception as e:
        print(f"[{timestamp}] 保存文本消息时出错: {e}")

def save_media_message(msg, chat, timestamp, download_path):
    """保存媒体消息信息"""
    try:
        media_info = f"媒体文件路径: {download_path}"
        log_message_to_file(f"[{msg.type.upper()}] {chat}", media_info, timestamp)
        print(f"[{timestamp}] 媒体消息已记录")

    except Exception as e:
        print(f"[{timestamp}] 保存媒体消息时出错: {e}")

def log_message_to_file(source, content, timestamp):
    """统一的消息日志记录函数"""
    try:
        log_file = 'F:/Win/openlist.txt'
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {source}: {content}\n")
    except Exception as e:
        print(f"记录消息到日志文件时出错: {e}")

# 添加监听，监听到的消息用on_message函数进行处理
wx.AddListenChat(nickname="启迪", callback=on_message)

print("微信消息监听已启动...")
print("支持的消息类型:")
print("- 普通文本消息")
print("- 聊天记录消息（将单独保存到chat_records目录）")
print("- 图片和视频消息（自动下载）")
print("按 Ctrl+C 停止监听")

try:
    # 保持程序运行
    wx.KeepRunning()
except KeyboardInterrupt:
    print("\n正在停止监听...")
    wx.RemoveListenChat(nickname="启迪")
    print("监听已停止")