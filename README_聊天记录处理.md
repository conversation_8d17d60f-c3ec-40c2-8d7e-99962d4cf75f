# 微信聊天记录处理指南

## 概述

本项目提供了完整的微信聊天记录处理解决方案，能够自动识别、解析和保存微信中的聊天记录消息。

## 功能特性

### 🔍 聊天记录自动识别
- 自动检测 `[聊天记录]` 类型的消息
- 支持多种聊天记录格式识别
- 智能判断消息是否为聊天记录

### 📝 聊天记录解析
- 提取聊天参与者信息
- 解析消息时间戳
- 分离个人消息内容
- 提取元数据信息

### 💾 多格式保存
- **文本格式**: 易于阅读的格式化文本
- **JSON格式**: 结构化数据，便于程序处理
- **分类保存**: 按日期和来源分类保存

### 📊 消息统计
- 参与者数量统计
- 消息条数统计
- 字符数统计
- 时间范围分析

## 文件结构

```
wxlg/
├── index.py                    # 原始的简单监听器
├── enhanced_wechat_listener.py # 增强版监听器
├── chat_record_handler.py      # 聊天记录处理器
├── chat_records/              # 聊天记录保存目录
│   ├── *.txt                  # 文本格式的聊天记录
│   └── *.json                 # JSON格式的聊天记录
├── message_logs/              # 消息日志目录
│   ├── text_messages_*.txt    # 文本消息日志
│   ├── media_messages_*.txt   # 媒体消息日志
│   ├── other_messages_*.txt   # 其他消息日志
│   └── errors_*.txt           # 错误日志
└── README_聊天记录处理.md      # 本说明文档
```

## 使用方法

### 方法一：使用增强版监听器（推荐）

```python
# 运行增强版监听器
python enhanced_wechat_listener.py
```

### 方法二：使用原始监听器

```python
# 运行原始监听器（已更新支持聊天记录）
python index.py
```

### 方法三：单独使用聊天记录处理器

```python
from chat_record_handler import ChatRecordHandler

# 创建处理器
handler = ChatRecordHandler()

# 处理聊天记录消息
handler.process_chat_record_message(msg, "聊天窗口名称")
```

## 聊天记录处理流程

### 1. 消息识别
当接收到微信消息时，系统会自动检查：
- 消息内容是否包含 `[聊天记录]`
- 消息内容是否包含聊天记录相关关键词

### 2. 内容解析
对识别出的聊天记录进行解析：
```python
# 解析结果示例
{
    "timestamp": "2025-10-10T14:30:00",
    "participants": ["用户A", "用户B"],
    "messages": [
        {
            "time": "14:30",
            "content": "你好",
            "raw_line": "14:30 用户A: 你好"
        }
    ],
    "metadata": {
        "date": "2025年10月10日",
        "total_lines": 15,
        "total_chars": 500
    }
}
```

### 3. 文件保存
聊天记录会被保存为两种格式：

#### 文本格式 (.txt)
```
聊天记录保存时间: 2025-10-10T14:30:00
来源聊天: 启迪
参与者: 用户A, 用户B
============================================================

原始内容:
[聊天记录]用户A与用户B的聊天记录
...

============================================================

解析后的消息:
1. [14:30] 你好
2. [14:31] 你好，有什么可以帮助你的吗？
...
```

#### JSON格式 (.json)
```json
{
    "timestamp": "2025-10-10T14:30:00",
    "raw_content": "[聊天记录]...",
    "participants": ["用户A", "用户B"],
    "messages": [...],
    "metadata": {...}
}
```

## 配置选项

### 修改监听对象
```python
# 在 enhanced_wechat_listener.py 中修改
listener = EnhancedWeChatListener(target_nickname="你的好友昵称")
```

### 修改保存目录
```python
# 在创建处理器时指定目录
handler = ChatRecordHandler(save_directory="自定义目录")
```

### 关闭自动回复
```python
# 在 on_message 方法中注释掉自动回复部分
# if isinstance(msg, FriendMessage):
#     msg.quote('收到')
```

## 支持的聊天记录格式

### 格式1：标准聊天记录
```
[聊天记录]用户A与用户B的聊天记录

2025年10月10日 14:30
用户A: 你好
14:31
用户B: 你好，有什么可以帮助你的吗？
```

### 格式2：简化时间格式
```
[聊天记录]
14:30 用户A: 你好
14:31 用户B: 你好，有什么可以帮助你的吗？
```

### 格式3：包含日期的格式
```
用户A与用户B的聊天记录
10月10日 14:30 用户A: 你好
10月10日 14:31 用户B: 你好，有什么可以帮助你的吗？
```

## 常见问题

### Q: 聊天记录没有被识别？
A: 检查消息内容是否包含聊天记录标识符，可以在 `ChatRecordHandler.is_chat_record()` 方法中添加更多识别模式。

### Q: 解析结果不准确？
A: 聊天记录的格式可能与预期不同，可以查看保存的原始内容，然后调整解析规则。

### Q: 文件保存失败？
A: 检查文件路径权限，确保程序有写入权限。

### Q: 如何处理特殊字符？
A: 程序会自动清理文件名中的特殊字符，确保文件名合法。

## 扩展功能

### 添加新的识别模式
```python
# 在 ChatRecordHandler.is_chat_record() 中添加
chat_record_indicators.append("你的新模式")
```

### 自定义解析规则
```python
# 继承 ChatRecordHandler 并重写解析方法
class CustomChatRecordHandler(ChatRecordHandler):
    def _extract_messages(self, content):
        # 你的自定义解析逻辑
        pass
```

### 添加数据库存储
```python
# 在保存方法中添加数据库操作
def save_to_database(self, parsed_data):
    # 数据库存储逻辑
    pass
```

## 注意事项

1. **隐私保护**: 聊天记录包含敏感信息，请妥善保管保存的文件
2. **文件管理**: 定期清理旧的日志文件，避免占用过多磁盘空间
3. **性能考虑**: 大量聊天记录可能影响处理速度，可考虑异步处理
4. **错误处理**: 程序包含完整的错误处理机制，错误信息会记录到日志文件

## 更新日志

- **v1.0**: 基础聊天记录识别和保存功能
- **v1.1**: 添加消息解析和结构化存储
- **v1.2**: 增强错误处理和日志记录
- **v1.3**: 添加统计功能和多格式支持

## 技术支持

如果遇到问题或需要新功能，请：
1. 查看错误日志文件
2. 检查聊天记录的原始格式
3. 根据需要调整解析规则
